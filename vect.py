# %%
import pandas as pd
import pandas_ta as ta # utilisé via df.ta
import vectorbt as vbt
import numpy as np
import plotly.graph_objects as go
from exchange import fetch_and_prepare_data

# Configure vectorbt for better visualization
vbt.settings.set_theme('dark')
vbt.settings['plotting']['layout']['template'] = 'plotly_dark'
vbt.settings['plotting']['layout']['width'] = 1200
vbt.settings['plotting']['layout']['height'] = 200

# %%
# ===== CONFIGURATION =====
# Mean reversion strategy parameters
BBANDS_PERIOD = 20         # Period for Bollinger Bands
BBANDS_STD = 2.0           # Standard deviation for Bollinger Bands
ADX_PERIOD = 14            # Period for ADX calculation
ADX_THRESHOLD = 20         # ADX threshold for trend strength
ADX_THRESHOLD_FILTER = 60  # Filter to avoid trading in directional markets
SMA_PERIOD = 200           # Period for SMA calculation
SMA_BOUNCE_THRESHOLD = 0.002  # Threshold for price bounce from SMA (0.2%)
INITIAL_ENTRY_SIZE = 0.01  # Initial position size (1% of portfolio)
DCA_SIZE_INCREMENT = 0.01  # Increment for each DCA (1% additional)
MAX_DCA_SIZE = 0.10  # Maximum DCA size (10% of portfolio)
EXIT_SIZE = 1.0            # Exit size (100% of position)
TF = '1h'
# Portfolio settings
INITIAL_CASH = 500000
FEE = 0.0004
# NEW SIZING / RISK PARAMETERS
ATR_PERIOD = 14          # ATR window for volatility
ATR_MULT   = 1.0         # Multiple of ATR used as stop distance
RISK_PCT   = 0.02        # % of portfolio equity risked per entry
# NEW: Maximum exposure allowed per side (long or short) at any time
MAX_SIDE_EXPOSURE = 0.30  # 30% of portfolio

# %%
# ===== DATA PREPARATION =====
SYMBOL = "ETH/USDC:USDC"
data = fetch_and_prepare_data(SYMBOL, TF)
# data = pd.read_csv("data/BTCUSD_1h_Combined_Index.csv").iloc[:,:5][90000:]

data.columns = [c.lower() for c in data.columns]

print(data)

required_cols = ['close', 'low', 'high']
if not all(col in data.columns for col in required_cols):
    raise ValueError(f"Missing required columns. Got: {data.columns.tolist()}")


# %%

# ===== TECHNICAL INDICATORS =====
# Add indicators using pandas_ta
# 1. Bollinger Bands
data.ta.bbands(length=BBANDS_PERIOD, std=BBANDS_STD, append=True)

# 2. ADX - Average Directional Index for trend strength
data.ta.adx(length=ADX_PERIOD, append=True)

# 3. SMA 200 - Support/Resistance level
data.ta.sma(length=SMA_PERIOD, append=True)
# NEW: ATR for volatility-based sizing
data.ta.atr(length=ATR_PERIOD, append=True)

# Clean up NaN values
data.dropna(inplace=True)
data.reset_index(drop=True, inplace=True)  # Reset index after dropping NaNs

# Define column names for readability
bbl_col = f'BBL_{BBANDS_PERIOD}_{BBANDS_STD}'
bbm_col = f'BBM_{BBANDS_PERIOD}_{BBANDS_STD}'
bbu_col = f'BBU_{BBANDS_PERIOD}_{BBANDS_STD}'
adx_col = f'ADX_{ADX_PERIOD}'
sma_col = f'SMA_{SMA_PERIOD}'
# Column name for ATR (detect dynamically because pandas_ta may change format)
atr_candidates = [col for col in data.columns if col.upper().startswith('ATR')]
if not atr_candidates:
    raise ValueError("ATR column not found after indicator calculation. Check pandas_ta version.")
atr_col = atr_candidates[0]

print(data)

# Print sample of calculated indicators
print("\n===== Technical Indicators =====")
indicator_cols = [bbl_col, bbm_col, bbu_col, adx_col, sma_col]
print(data[['close'] + indicator_cols].tail(20))

# %%
# ===== ENTRY/EXIT SIGNALS =====
# --- LONG & SHORT STRATEGIES ---
# Trend condition for adaptive exits
weak_trend = data[adx_col] < ADX_THRESHOLD
strong_trend = ~weak_trend  # Opposite of weak trend
# Filter condition for high ADX - avoid trading in strongly directional markets
high_adx_filter = data[adx_col] >= ADX_THRESHOLD_FILTER

# Define entry conditions
# Initial entries: Price crosses from below to above the lower band for longs
# AND ADX is below the filter threshold
long_initial_entries = (data['close'].shift(1) < data[bbl_col].shift(1)) & (data['close'] >= data[bbl_col]) & (~high_adx_filter)

# DCA conditions for longs: Price touches or goes below lower band
# AND ADX is below the filter threshold
long_dca_conditions = (data['low'] <= data[bbl_col]) & (~high_adx_filter)

# Initial entries: Price crosses from above to below the upper band for shorts
# AND ADX is below the filter threshold
short_initial_entries = (data['close'].shift(1) > data[bbu_col].shift(1)) & (data['close'] <= data[bbu_col]) & (~high_adx_filter)

# DCA conditions for shorts: Price touches or goes above upper band
# AND ADX is below the filter threshold
short_dca_conditions = (data['high'] >= data[bbu_col]) & (~high_adx_filter)

# Initialize arrays for position tracking
long_position = pd.Series(0, index=data.index)  # 0 = no position, 1 = in position
short_position = pd.Series(0, index=data.index)  # 0 = no position, 1 = in position

# Initialize signal arrays
long_entries = pd.Series(False, index=data.index)
short_entries = pd.Series(False, index=data.index)
long_exits = pd.Series(False, index=data.index)
short_exits = pd.Series(False, index=data.index)

# Track positions, DCA opportunities, and generate clean signals in a single pass
for i in range(len(data)):
    # Carry forward position state from previous bar (if not first bar)
    if i > 0:
        long_position.iloc[i] = long_position.iloc[i-1]
        short_position.iloc[i] = short_position.iloc[i-1]
        # NEW: Prevent overlapping exposure – close the opposite side if a fresh initial entry appears
        if long_position.iloc[i] > 0 and short_initial_entries.iloc[i]:
            long_exits.iloc[i] = True
            long_position.iloc[i] = 0  # Close long before opening short
        elif short_position.iloc[i] > 0 and long_initial_entries.iloc[i]:
            short_exits.iloc[i] = True
            short_position.iloc[i] = 0  # Close short before opening long
    
    # Define exit conditions - UPDATED to exit on:
    # 1. Price reaching the opposite band
    # 2. Price reaching middle line if ADX is low (weak trend)
    # 3. ADX exceeding the filter threshold (strong directional market)
    long_exit_condition = (data['close'].iloc[i] >= data[bbu_col].iloc[i]) | \
                          ((data['close'].iloc[i] >= data[bbm_col].iloc[i]) & weak_trend.iloc[i]) | \
                          high_adx_filter.iloc[i]  # Exit if ADX exceeds filter threshold
    
    short_exit_condition = (data['close'].iloc[i] <= data[bbl_col].iloc[i]) | \
                           ((data['close'].iloc[i] <= data[bbm_col].iloc[i]) & weak_trend.iloc[i]) | \
                           high_adx_filter.iloc[i]  # Exit if ADX exceeds filter threshold
    
    # Process positions in priority order: exits first, then entries
    
    # Process exits (only if we have a position)
    if long_position.iloc[i] > 0 and long_exit_condition:
        long_exits.iloc[i] = True
        long_position.iloc[i] = 0  # Close position
    
    if short_position.iloc[i] > 0 and short_exit_condition:
        short_exits.iloc[i] = True
        short_position.iloc[i] = 0  # Close position
    
    # Process initial entries (only if we don't have a position)
    if long_position.iloc[i] == 0 and long_initial_entries.iloc[i]:
        long_entries.iloc[i] = True
        long_position.iloc[i] = 1  # Open position
    
    if short_position.iloc[i] == 0 and short_initial_entries.iloc[i]:
        short_entries.iloc[i] = True
        short_position.iloc[i] = 1  # Open position
    
    # Process DCA opportunities (only if we already have a position and no exit signal on same bar)
    if long_position.iloc[i] > 0 and not long_exits.iloc[i] and long_dca_conditions.iloc[i]:
        # Only add DCA if we didn't already have an entry on this bar
        if not long_entries.iloc[i]:
            long_entries.iloc[i] = True  # Add to position
    
    if short_position.iloc[i] > 0 and not short_exits.iloc[i] and short_dca_conditions.iloc[i]:
        # Only add DCA if we didn't already have an entry on this bar
        if not short_entries.iloc[i]:
            short_entries.iloc[i] = True  # Add to position

# Count entries and exits by type
long_initial_count = sum(long_initial_entries & long_entries)
long_dca_count = sum(long_entries) - long_initial_count
short_initial_count = sum(short_initial_entries & short_entries)
short_dca_count = sum(short_entries) - short_initial_count


# Create simplified entry tracking
entry_counts = pd.DataFrame({
    'Count': [
        sum(long_entries),
        sum(short_entries)
    ]
}, index=['Long', 'Short'])

# Plot distribution of long vs short entries
# entry_counts.plot.pie(y='Count', autopct='%1.1f%%')
 
# Print signal statistics
print("\n===== Signal Statistics =====")
print(f"Long entries: {long_initial_count}")
print(f"Long exits: {sum(long_exits)}")
print(f"Short entries: {short_initial_count}")
print(f"Short exits: {sum(short_exits)}")


# %%
# ===== VECTORIZED SIZING CALCULATION =====
def calculate_atr_based_size_vectorized(entries, exits, close, atr, initial_cash, risk_pct, atr_mult,
                                       dca_increment, max_dca_size, max_exposure_pct):
    """Fully vectorized calculation of position sizes based on ATR and risk management"""
    
    # 1. Calculate risk and base size
    risk_value = risk_pct * initial_cash
    base_size = np.where(
        (atr > 0) & ~np.isnan(atr),
        risk_value / (atr_mult * atr),  # Size in number of units (e.g., ETH)
        0
    )
    base_size_value = base_size * close  # Size in monetary value (e.g., USDC)
    
    # 2. Vectorize DCA counter - FIXED
    # A new trade starts after each exit. Use cumsum() to create trade ID.
    trade_id = exits.cumsum().shift(1).fillna(0)
    
    # Only count entries when signal is True
    entries_only = entries.where(entries, 0).astype(int)
    
    # Group by trade ID and do cumulative sum to get DCA counter
    dca_count = entries_only.groupby(trade_id).cumsum()
    
    # 3. Apply DCA scaling logic
    # Size increases with (dca_counter - 1) * increment
    dca_multiplier = 1 + (dca_count - 1) * dca_increment
    
    # Calculate size with DCA scaling
    scaled_size_value = base_size_value * dca_multiplier
    
    # 4. Apply caps
    max_dca_value = max_dca_size * initial_cash
    max_exposure_value = max_exposure_pct * initial_cash
    
    # Cap each individual entry
    final_size = scaled_size_value.clip(upper=max_dca_value)
    final_size = final_size.clip(upper=max_exposure_value)
    
    # Ensure size is 0 where there's no entry
    final_size[~entries] = 0
    
    return final_size

# Calculate sizes for both long and short using vectorized function
long_size = calculate_atr_based_size_vectorized(
    long_entries, long_exits, data['close'], data[atr_col],
    INITIAL_CASH, RISK_PCT, ATR_MULT, DCA_SIZE_INCREMENT, MAX_DCA_SIZE, MAX_SIDE_EXPOSURE
)

short_size = calculate_atr_based_size_vectorized(
    short_entries, short_exits, data['close'], data[atr_col],
    INITIAL_CASH, RISK_PCT, ATR_MULT, DCA_SIZE_INCREMENT, MAX_DCA_SIZE, MAX_SIDE_EXPOSURE
)

# Combine sizes both positive
size_array = long_size + short_size  # This creates the correct sign convention

print("\n===== Portfolio Usage Statistics =====")
print(f"Maximum long size used: {long_size.max() / INITIAL_CASH:.2%}")
print(f"Maximum short size used: {short_size.max() / INITIAL_CASH:.2%}")
print(f"Maximum combined exposure: {(long_size + short_size).max() / INITIAL_CASH:.2%}")

print("\n===== Risk Management Validation =====")
print(f"ATR-based stop distance: {ATR_MULT} * ATR")
print(f"Risk per trade: {RISK_PCT:.1%} of portfolio")
print("✅ Stop-loss now integrated in portfolio - risk calculation aligned with exits")

# ===== PORTFOLIO CREATION =====
# Create a single portfolio that handles both long and short positions using vectorbt's built-in functionality
# CRITICAL FIX: Add explicit stop-loss based on ATR to align with risk calculation
portfolio = vbt.Portfolio.from_signals(
    close=data['close'],
    entries=long_entries,                # Long entries
    exits=long_exits,                    # Long exits
    short_entries=short_entries,         # Short entries
    short_exits=short_exits,             # Short exits
    size=size_array,                     # Variable position size based on ATR
    size_type='value',                   # Size expressed in dollar value
    # --- CRITICAL: ADD STOP-LOSS BASED ON ATR ---
    sl_stop=data[atr_col] * ATR_MULT / data['close'],  # Stop in % of ATR distance
    sl_trail=False,                      # Set True for trailing stop
    # --------------------------------------------
    init_cash=INITIAL_CASH,
    fees=FEE,
    freq=TF,
    accumulate=True                      # Allow DCA (mean reversion strategy uses multiple entries)
)

# %%
# ===== STATS =====
print("\n===== Portfolio Stats =====")
stats = portfolio.stats()

def calculate_beta(strategy_ret: pd.Series,
                   benchmark_ret: pd.Series) -> float:
    """
    Beta = Cov(strategy, benchmark) / Var(benchmark).

    Les deux séries doivent être déjà exprimées en rendements
    (pourcentage ou log-retours) et indexées sur les mêmes dates.
    """
    aligned = pd.concat([strategy_ret, benchmark_ret], axis=1).dropna()
    if aligned.shape[0] < 2:
        return np.nan
    cov = np.cov(aligned.iloc[:, 0], aligned.iloc[:, 1])[0, 1]
    var = np.var(aligned.iloc[:, 1])
    return cov / var if var > 0 else np.nan

# Calcul du beta et ajout aux stats
strategy_returns = portfolio.returns()
asset_returns = data['close'].pct_change()
beta = calculate_beta(strategy_returns, asset_returns)

# Création d'une série pandas pour le beta avec le même format que les autres stats
beta_stat = pd.Series(beta, index=['Beta vs Benchmark'])

stats = pd.concat([stats, beta_stat])

print(stats)

# Analyze order execution to see if positions close in a single bar
print("\n===== Portfolio Trade Analysis =====")
print(f"Total trades: {len(portfolio.trades)}")

# %%
# ===== UNIFIED STRATEGY DASHBOARD =====
def create_strategy_dashboard(portfolio, data, indicators):
    """
    Advanced dashboard with vectorbt plotting features similar to orbbbb.py
    """
    
    print(f"\n{'='*60}")
    print(f"📊 ADVANCED STRATEGY DASHBOARD - {SYMBOL}")
    print(f"{'='*60}")
    
    # Start with portfolio plotting using advanced features
    fig = portfolio.plot(
        subplots=['trade_pnl', 'orders', 'drawdowns'],
        make_subplots_kwargs={
            'vertical_spacing': 0.05
        }
    )
    
    # Add OHLC data with advanced styling
    fig = data.vbt.ohlcv.plot(
        plot_type='candlestick',
        fig=fig,
        show_volume=False,
        xaxis_rangeslider_visible=False    )

    # Add indicators with advanced styling
    colors = ['yellow', 'orange', 'cyan', 'magenta']
    for i, col in enumerate(indicators.columns):
        fig.add_trace(
            go.Scatter(
                x=indicators.index,
                y=indicators[col],
                mode='lines',
                name=col,
                line=dict(color=colors[i % len(colors)], width=1.5),
                opacity=0.8
            ),
            row=1, col=0
        )
  
    # Advanced layout configuration
    fig.update_layout(
        height=1400,
        width=None,
        title=f'📊 {SYMBOL} - Advanced Strategy Analysis'    )
    
    fig.show()    

# Prepare indicator data for plotting
indicators = data[[bbl_col, bbm_col, bbu_col, sma_col]].rename(columns={
    bbl_col: 'Lower BB', 
    bbm_col: 'Middle BB', 
    bbu_col: 'Upper BB',
    sma_col: 'SMA 200'
})

# Create comprehensive dashboard
create_strategy_dashboard(portfolio, data, indicators)


# %%
# ===== STRATEGY OPTIMIZATION (OPTIONAL) =====
def run_parameter_sweep():
    """
    Optional parameter optimization - uncomment to run
    Tests different Bollinger Band and ADX parameters
    """
    param_ranges = {
        'bb_periods': [15, 20, 25],
        'bb_stds': [1.5, 2.0, 2.5],
        'adx_thresholds': [15, 20, 25]
    }
    
    results = {}
    print("\n🔍 Running parameter sweep...")
    
    for bb_period in param_ranges['bb_periods']:
        for bb_std in param_ranges['bb_stds']:
            for adx_threshold in param_ranges['adx_thresholds']:
                try:
                    # Recalculate indicators
                    temp_data = data.copy()
                    temp_data.ta.bbands(length=bb_period, std=bb_std, append=True)
                    temp_data.ta.adx(length=ADX_PERIOD, append=True)
                    temp_data.dropna(inplace=True)
                    
                    # Generate simple signals
                    bbl = f'BBL_{bb_period}_{bb_std}'
                    bbu = f'BBU_{bb_period}_{bb_std}'
                    adx = f'ADX_{ADX_PERIOD}'
                    
                    if all(col in temp_data.columns for col in [bbl, bbu, adx]):
                        long_entries_test = (temp_data['close'] <= temp_data[bbl]) & (temp_data[adx] < ADX_THRESHOLD_FILTER)
                        long_exits_test = (temp_data['close'] >= temp_data[bbu]) | (temp_data[adx] >= ADX_THRESHOLD_FILTER)
                        
                        short_entries_test = (temp_data['close'] >= temp_data[bbu]) & (temp_data[adx] < ADX_THRESHOLD_FILTER)
                        short_exits_test = (temp_data['close'] <= temp_data[bbl]) | (temp_data[adx] >= ADX_THRESHOLD_FILTER)
                        
                        # Create test portfolio
                        test_pf = vbt.Portfolio.from_signals(
                            close=temp_data['close'],
                            entries=long_entries_test,
                            exits=long_exits_test,
                            short_entries=short_entries_test,
                            short_exits=short_exits_test,
                            init_cash=INITIAL_CASH,
                            fees=FEE,
                            freq=TF
                        )
                        
                        # Store results
                        test_stats = test_pf.stats()
                        param_name = f'BB{bb_period}_{bb_std}_ADX{adx_threshold}'
                        results[param_name] = {
                            'total_return': test_stats.get('Total Return [%]', 0),
                            'sharpe_ratio': test_stats.get('Sharpe Ratio', 0),
                            'max_drawdown': test_stats.get('Max Drawdown [%]', 0),
                            'win_rate': test_stats.get('Win Rate [%]', 0)
                        }
                        
                except Exception as e:
                    print(f"⚠️ Failed for BB{bb_period}_{bb_std}_ADX{adx_threshold}: {e}")
                    continue
    
    if results:
        results_df = pd.DataFrame(results).T
        print("\n📊 PARAMETER SWEEP RESULTS (Top 5 by Sharpe):")
        print(results_df.sort_values('sharpe_ratio', ascending=False).head())
        return results_df
    else:
        print("❌ No valid results from parameter sweep")
        return None

sweep_results = run_parameter_sweep()



